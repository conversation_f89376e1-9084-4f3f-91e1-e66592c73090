package com.cjy.pyp.modules.activity.controller;

import java.util.Arrays;
import java.util.Collections;
import java.util.Map;

import com.cjy.pyp.common.constant.RedisScriptConstant;
import com.cjy.pyp.common.constant.TokenConstant;
import com.cjy.pyp.common.exception.RRException;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;

import com.cjy.pyp.modules.activity.entity.ActivityImageEntity;
import com.cjy.pyp.modules.activity.service.ActivityImageService;
import com.cjy.pyp.modules.activity.dao.ActivityImagePlatformUsageDao;
import com.cjy.pyp.modules.activity.entity.ActivityImagePlatformUsageEntity;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import java.util.List;
import com.cjy.pyp.modules.sys.controller.AbstractController;
import com.cjy.pyp.common.utils.PageUtils;
import com.cjy.pyp.common.utils.R;

import javax.annotation.Resource;


/**
 * 
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-06-17 14:59:57
 */
@RestController
@RequestMapping("activity/activityimage")
public class ActivityImageController extends AbstractController {
    @Autowired
    private ActivityImageService activityImageService;
    @Autowired
    private ActivityImagePlatformUsageDao activityImagePlatformUsageDao;
    @Resource
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 列表
     */
    @RequestMapping("/list")
    @RequiresPermissions("activity:activityimage:list")
    public R list(@RequestParam Map<String, Object> params){
        PageUtils page = activityImageService.queryPage(params);

        return R.ok().put("page", page);
    }


    /**
     * 信息
     */
    @RequestMapping("/info/{id}")
    @RequiresPermissions("activity:activityimage:info")
    public R info(@PathVariable("id") Long id){
		ActivityImageEntity activityImage = activityImageService.getById(id);

        return R.ok().put("activityImage", activityImage);
    }

    /**
     * 保存
     */
    @RequestMapping("/save")
    @RequiresPermissions("activity:activityimage:save")
    @Transactional(rollbackFor = Exception.class)
    public R save(@RequestBody ActivityImageEntity activityImage){
        // 原子性操作验证和删除令牌
        // Long result = stringRedisTemplate.execute(new DefaultRedisScript<>(RedisScriptConstant.CHECK, Long.class),
        //         Collections.singletonList(TokenConstant.COMMON_TOKEN_PREFIX + getUserId()), activityImage.getRepeatToken());
        // if (result == 0L) {
        //     throw new RRException("不能重复提交");
        // }
		activityImageService.save(activityImage);

        return R.ok();
    }

    /**
     * 修改
     */
    @RequestMapping("/update")
    @RequiresPermissions("activity:activityimage:update")
    @Transactional(rollbackFor = Exception.class)
    public R update(@RequestBody ActivityImageEntity activityImage){
        // 原子性操作验证和删除令牌
        Long result = stringRedisTemplate.execute(new DefaultRedisScript<>(RedisScriptConstant.CHECK, Long.class),
                Collections.singletonList(TokenConstant.COMMON_TOKEN_PREFIX + getUserId()), activityImage.getRepeatToken());
        if (result == 0L) {
            throw new RRException("不能重复提交");
        }
		activityImageService.updateById(activityImage);

        return R.ok();
    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    @RequiresPermissions("activity:activityimage:delete")
    public R delete(@RequestBody Long[] ids){
		activityImageService.removeByIds(Arrays.asList(ids));

        return R.ok();
    }

    /**
     * 获取图片平台使用情况
     */
    @RequestMapping("/platform-usage")
    @RequiresPermissions("activity:activityimage:list")
    public R getPlatformUsage(@RequestParam("imageId") Long imageId) {
        try {
            // 使用MyBatis Plus查询平台使用记录
            List<ActivityImagePlatformUsageEntity> usageList = activityImagePlatformUsageDao.selectList(
                new QueryWrapper<ActivityImagePlatformUsageEntity>()
                    .eq("image_id", imageId)
                    .orderByDesc("last_used_time")
            );

            return R.ok().put("data", usageList);
        } catch (Exception e) {
            return R.error("获取平台使用数据失败: " + e.getMessage());
        }
    }

}
